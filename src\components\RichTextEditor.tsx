import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import { useEffect, useState, useCallback } from 'react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import History from '@tiptap/extension-history';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Minus,
  Undo,
  Redo
} from 'lucide-react';
import type { EditChange, UserRole } from '../types';
import { CollaborativeEditingService } from '../services/collaborativeEditingService';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  sceneId?: string;
  hideToolbar?: boolean;
  readOnly?: boolean;
  // Collaborative editing props
  bookId?: string;
  currentUserId?: string;
  currentUserName?: string;
  userRole?: UserRole;
  editChanges?: EditChange[];
  onAcceptChange?: (changeId: string) => void;
  onRejectChange?: (changeId: string) => void;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content,
  onChange,
  placeholder = "Start writing...",
  className = "",
  sceneId,
  hideToolbar = false,
  readOnly = false,
  // Collaborative editing props
  bookId,
  currentUserId,
  currentUserName,
  userRole = 'author',
  editChanges = [],
  onAcceptChange,
  onRejectChange
}) => {
  // Clear history when scene changes by forcing editor recreation
  const editorKey = sceneId || 'default';

  // State for tracking changes
  const [lastContent, setLastContent] = useState(content);
  const [isTracking, setIsTracking] = useState(false);

  // Change detection function
  const detectChange = useCallback((oldText: string, newText: string): Omit<EditChange, 'id' | 'userId' | 'userName' | 'timestamp' | 'isAccepted'> | null => {
    if (oldText === newText) return null;

    // Simple implementation - detect if text was added or removed
    if (newText.length > oldText.length) {
      // Text was inserted
      const insertPosition = findInsertPosition(oldText, newText);
      return {
        type: 'insert',
        position: insertPosition,
        originalText: '',
        newText: newText.substring(insertPosition, insertPosition + (newText.length - oldText.length))
      };
    } else if (newText.length < oldText.length) {
      // Text was deleted
      const deletePosition = findDeletePosition(oldText, newText);
      return {
        type: 'delete',
        position: deletePosition,
        originalText: oldText.substring(deletePosition, deletePosition + (oldText.length - newText.length)),
        newText: ''
      };
    } else {
      // Text was replaced (same length)
      const replacePosition = findReplacePosition(oldText, newText);
      return {
        type: 'replace',
        position: replacePosition,
        originalText: oldText.substring(replacePosition, replacePosition + 1),
        newText: newText.substring(replacePosition, replacePosition + 1)
      };
    }
  }, []);

  // Helper functions for change detection
  const findInsertPosition = (oldText: string, newText: string): number => {
    for (let i = 0; i < Math.min(oldText.length, newText.length); i++) {
      if (oldText[i] !== newText[i]) {
        return i;
      }
    }
    return oldText.length;
  };

  const findDeletePosition = (oldText: string, newText: string): number => {
    for (let i = 0; i < Math.min(oldText.length, newText.length); i++) {
      if (oldText[i] !== newText[i]) {
        return i;
      }
    }
    return newText.length;
  };

  const findReplacePosition = (oldText: string, newText: string): number => {
    for (let i = 0; i < Math.min(oldText.length, newText.length); i++) {
      if (oldText[i] !== newText[i]) {
        return i;
      }
    }
    return 0;
  };

  // Handle content changes with collaborative tracking
  const handleContentChange = useCallback(async (newContent: string) => {
    // If user is an editor (not author), track the change
    if (userRole === 'editor' && bookId && sceneId && currentUserId && currentUserName && !isTracking) {
      setIsTracking(true);
      const plainOldText = lastContent.replace(/<[^>]*>/g, ''); // Strip HTML for comparison
      const plainNewText = newContent.replace(/<[^>]*>/g, '');

      const change = detectChange(plainOldText, plainNewText);
      if (change) {
        try {
          await CollaborativeEditingService.trackEditChange(bookId, sceneId, {
            userId: currentUserId,
            userName: currentUserName,
            type: change.type,
            position: change.position,
            originalText: change.originalText,
            newText: change.newText,
            isAccepted: null
          });
        } catch (error) {
          console.error('Error tracking change:', error);
        }
      }
      setIsTracking(false);
    }

    setLastContent(newContent);
    onChange(newContent);
  }, [bookId, sceneId, currentUserId, currentUserName, userRole, lastContent, isTracking, onChange, detectChange]);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        codeBlock: false,
        blockquote: false,
        horizontalRule: false,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      HorizontalRule,
      History.configure({
        depth: 50,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      handleContentChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: `prose prose-lg max-w-none focus:outline-none text-gray-900 leading-relaxed ${userRole === 'editor' ? 'editor-mode' : ''}`,
        style: 'font-family: "Crimson Text", serif; font-size: 16pt; line-height: 1.6;',
        placeholder,
      },
    },
    editable: !readOnly,
  }, [editorKey, handleContentChange, userRole]);

  // Render content with tracked changes highlighted (for authors viewing editor changes)
  const renderContentWithChanges = useCallback(() => {
    if (userRole !== 'author' || !editChanges.length) {
      return content;
    }

    let renderedContent = content;
    const pendingEditChanges = editChanges.filter(change => change.isAccepted === null);

    // Apply visual styling for pending changes
    pendingEditChanges.forEach(change => {
      if (change.type === 'insert' || change.type === 'replace') {
        const beforeText = renderedContent.substring(0, change.position);
        const afterText = renderedContent.substring(change.position + change.originalText.length);

        const changeElement = `<span class="edit-change edit-${change.type}" data-change-id="${change.id}" data-user="${change.userName}" style="background-color: #fef3c7; border-left: 3px solid #f59e0b; padding: 2px 4px; margin: 0 2px; border-radius: 3px; position: relative;">
          ${change.newText}
          <span class="change-tooltip" style="position: absolute; bottom: 100%; left: 0; background: #374151; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; white-space: nowrap; opacity: 0; pointer-events: none; transition: opacity 0.2s;">
            Edit by ${change.userName}
          </span>
        </span>`;

        renderedContent = beforeText + changeElement + afterText;
      }
    });

    return renderedContent;
  }, [content, editChanges, userRole]);

  // Update editor content when the content prop changes (but not from editor updates)
  useEffect(() => {
    if (editor) {
      const contentToSet = userRole === 'author' ? renderContentWithChanges() : content;
      if (contentToSet !== editor.getHTML()) {
        editor.commands.setContent(contentToSet, false);
        setLastContent(contentToSet);
      }
    }
  }, [content, editor, renderContentWithChanges, userRole]);
  if (!editor) {
    return null;
  }

  const ToolbarButton: React.FC<{
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title?: string;
  }> = ({ onClick, isActive = false, disabled = false, children, title }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`p-2 rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-600'
          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );



  return (
    <div key={editorKey} className={`bg-white ${className.includes('focus-mode') ? '' : 'rounded-2xl shadow-2xl'} ${className} flex flex-col h-full ${readOnly ? 'read-only-editor' : ''} ${userRole === 'editor' ? 'border-l-4 border-blue-500' : ''}`}>
      {/* Add global CSS for editor highlighting */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .edit-change {
            background-color: #fef3c7;
            border-left: 3px solid #f59e0b;
            padding: 2px 4px;
            margin: 0 2px;
            border-radius: 3px;
            position: relative;
          }
          .edit-change:hover .change-tooltip {
            opacity: 1 !important;
          }
          .edit-change.edit-insert {
            background-color: #dcfce7;
            border-left-color: #16a34a;
          }
          .edit-change.edit-replace {
            background-color: #fef3c7;
            border-left-color: #f59e0b;
          }
          .edit-change.edit-delete {
            background-color: #fee2e2;
            border-left-color: #dc2626;
            text-decoration: line-through;
          }
          .change-tooltip {
            position: absolute;
            bottom: 100%;
            left: 0;
            background: #374151;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
          }
        `
      }} />

      {/* Role Indicator */}
      {userRole && userRole !== 'author' && (
        <div className="px-4 py-2 bg-blue-50 border-b border-blue-200 text-sm">
          <span className="text-blue-800 font-medium">
            Editing as: <span className="capitalize">{userRole}</span>
          </span>
          {userRole === 'editor' && (
            <span className="ml-2 text-blue-600">• Your changes will be highlighted for review</span>
          )}
        </div>
      )}

      {/* Toolbar */}
      {!hideToolbar && !readOnly && (
        <div className="border-b border-gray-200 p-4">
        <div className="flex flex-wrap gap-1">
          {/* Text Formatting */}
          <div className="flex gap-1 mr-4">
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title="Bold"
            >
              <Bold className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title="Italic"
            >
              <Italic className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              isActive={editor.isActive('underline')}
              title="Underline"
            >
              <UnderlineIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleStrike().run()}
              isActive={editor.isActive('strike')}
              title="Strikethrough"
            >
              <Strikethrough className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Alignment */}
          <div className="flex gap-1 mr-4">
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              isActive={editor.isActive({ textAlign: 'left' })}
              title="Align Left"
            >
              <AlignLeft className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              isActive={editor.isActive({ textAlign: 'center' })}
              title="Align Center"
            >
              <AlignCenter className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              isActive={editor.isActive({ textAlign: 'right' })}
              title="Align Right"
            >
              <AlignRight className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('justify').run()}
              isActive={editor.isActive({ textAlign: 'justify' })}
              title="Justify"
            >
              <AlignJustify className="w-4 h-4" />
            </ToolbarButton>
          </div>



          {/* Divider */}
          <div className="flex gap-1 mr-4">
            <ToolbarButton
              onClick={() => editor.chain().focus().setHorizontalRule().run()}
              title="Add Divider"
            >
              <Minus className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* History */}
          <div className="flex gap-1">
            <ToolbarButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              title="Undo"
            >
              <Undo className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              title="Redo"
            >
              <Redo className="w-4 h-4" />
            </ToolbarButton>
          </div>
        </div>
        </div>
      )}

      {/* Editor */}
      <div className="p-8 flex-1 overflow-y-auto min-h-0">
        <EditorContent editor={editor} className="h-full" />
      </div>
    </div>
  );
};