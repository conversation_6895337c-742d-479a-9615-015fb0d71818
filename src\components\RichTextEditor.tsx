import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import { useEffect, useState, useCallback, useRef } from 'react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import History from '@tiptap/extension-history';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Minus,
  Undo,
  Redo
} from 'lucide-react';
import type { EditChange, UserRole } from '../types';
import { CollaborativeEditingService } from '../services/collaborativeEditingService';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  sceneId?: string;
  hideToolbar?: boolean;
  readOnly?: boolean;
  // Collaborative editing props
  bookId?: string;
  currentUserId?: string;
  currentUserName?: string;
  userRole?: UserRole;
  editChanges?: EditChange[];
  onAcceptChange?: (changeId: string) => void;
  onRejectChange?: (changeId: string) => void;
}

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content,
  onChange,
  placeholder = "Start writing...",
  className = "",
  sceneId,
  hideToolbar = false,
  readOnly = false,
  // Collaborative editing props
  bookId,
  currentUserId,
  currentUserName,
  userRole = 'author',
  editChanges = [],
  onAcceptChange,
  onRejectChange
}) => {
  // Clear history when scene changes by forcing editor recreation
  const editorKey = sceneId || 'default';

  // State for tracking changes with debouncing
  const [lastSavedContent, setLastSavedContent] = useState(content);
  const changeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTrackingRef = useRef(false);

  // Debounced change tracking - only track after user stops typing
  const debouncedTrackChange = useCallback(async (newContent: string) => {
    // Clear any existing timeout
    if (changeTimeoutRef.current) {
      clearTimeout(changeTimeoutRef.current);
    }

    // Set a new timeout to track the change after 2 seconds of inactivity
    changeTimeoutRef.current = setTimeout(async () => {
      if (userRole === 'editor' && bookId && sceneId && currentUserId && currentUserName && !isTrackingRef.current) {
        isTrackingRef.current = true;

        const plainOldText = lastSavedContent.replace(/<[^>]*>/g, ''); // Strip HTML for comparison
        const plainNewText = newContent.replace(/<[^>]*>/g, '');

        // Only track if there's a meaningful change
        if (plainOldText !== plainNewText && plainNewText.trim().length > 0) {
          try {
            // Simple change detection - just track as an edit
            await CollaborativeEditingService.trackEditChange(bookId, sceneId, {
              userId: currentUserId,
              userName: currentUserName,
              type: 'replace',
              position: 0,
              originalText: plainOldText,
              newText: plainNewText,
              isAccepted: null
            });
            setLastSavedContent(newContent);
          } catch (error) {
            console.error('Error tracking change:', error);
          }
        }

        isTrackingRef.current = false;
      }
    }, 2000); // 2 second delay
  }, [bookId, sceneId, currentUserId, currentUserName, userRole, lastSavedContent]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (changeTimeoutRef.current) {
        clearTimeout(changeTimeoutRef.current);
      }
    };
  }, []);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        codeBlock: false,
        blockquote: false,
        horizontalRule: false,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      HorizontalRule,
      History.configure({
        depth: 50,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const newContent = editor.getHTML();
      // Call onChange immediately for responsive typing
      onChange(newContent);
      // Track changes with debouncing for editors
      if (userRole === 'editor') {
        debouncedTrackChange(newContent);
      }
    },
    editorProps: {
      attributes: {
        class: `prose prose-lg max-w-none focus:outline-none text-gray-900 leading-relaxed ${userRole === 'editor' ? 'editor-mode' : ''}`,
        style: 'font-family: "Crimson Text", serif; font-size: 16pt; line-height: 1.6;',
        placeholder,
      },
    },
    editable: !readOnly,
  }, [editorKey, onChange, userRole, debouncedTrackChange]);

  // For now, just return content as-is to avoid performance issues
  // TODO: Implement proper change highlighting later
  const renderContentWithChanges = useCallback(() => {
    return content;
  }, [content]);

  // Update editor content when the content prop changes (but not from editor updates)
  useEffect(() => {
    if (editor) {
      const contentToSet = userRole === 'author' ? renderContentWithChanges() : content;
      if (contentToSet !== editor.getHTML()) {
        editor.commands.setContent(contentToSet, false);
        setLastSavedContent(contentToSet);
      }
    }
  }, [content, editor, renderContentWithChanges, userRole]);
  if (!editor) {
    return null;
  }

  const ToolbarButton: React.FC<{
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title?: string;
  }> = ({ onClick, isActive = false, disabled = false, children, title }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`p-2 rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-100 text-blue-600'
          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );



  return (
    <div key={editorKey} className={`bg-white ${className.includes('focus-mode') ? '' : 'rounded-2xl shadow-2xl'} ${className} flex flex-col h-full ${readOnly ? 'read-only-editor' : ''} ${userRole === 'editor' ? 'border-l-4 border-blue-500' : ''}`}>

      {/* Role Indicator */}
      {userRole && userRole !== 'author' && (
        <div className="px-4 py-2 bg-blue-50 border-b border-blue-200 text-sm">
          <span className="text-blue-800 font-medium">
            Editing as: <span className="capitalize">{userRole}</span>
          </span>
          {userRole === 'editor' && (
            <span className="ml-2 text-blue-600">• Your changes will be highlighted for review</span>
          )}
        </div>
      )}

      {/* Toolbar */}
      {!hideToolbar && !readOnly && (
        <div className="border-b border-gray-200 p-4">
        <div className="flex flex-wrap gap-1">
          {/* Text Formatting */}
          <div className="flex gap-1 mr-4">
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title="Bold"
            >
              <Bold className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title="Italic"
            >
              <Italic className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              isActive={editor.isActive('underline')}
              title="Underline"
            >
              <UnderlineIcon className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleStrike().run()}
              isActive={editor.isActive('strike')}
              title="Strikethrough"
            >
              <Strikethrough className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* Alignment */}
          <div className="flex gap-1 mr-4">
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              isActive={editor.isActive({ textAlign: 'left' })}
              title="Align Left"
            >
              <AlignLeft className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              isActive={editor.isActive({ textAlign: 'center' })}
              title="Align Center"
            >
              <AlignCenter className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              isActive={editor.isActive({ textAlign: 'right' })}
              title="Align Right"
            >
              <AlignRight className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('justify').run()}
              isActive={editor.isActive({ textAlign: 'justify' })}
              title="Justify"
            >
              <AlignJustify className="w-4 h-4" />
            </ToolbarButton>
          </div>



          {/* Divider */}
          <div className="flex gap-1 mr-4">
            <ToolbarButton
              onClick={() => editor.chain().focus().setHorizontalRule().run()}
              title="Add Divider"
            >
              <Minus className="w-4 h-4" />
            </ToolbarButton>
          </div>

          {/* History */}
          <div className="flex gap-1">
            <ToolbarButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              title="Undo"
            >
              <Undo className="w-4 h-4" />
            </ToolbarButton>
            <ToolbarButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              title="Redo"
            >
              <Redo className="w-4 h-4" />
            </ToolbarButton>
          </div>
        </div>
        </div>
      )}

      {/* Editor */}
      <div className="p-8 flex-1 overflow-y-auto min-h-0">
        <EditorContent editor={editor} className="h-full" />
      </div>
    </div>
  );
};